#!/bin/bash

echo "🧪 Running Biormika Backend Tests..."

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Set test environment
export ENV=test

# Run unit tests
echo -e "${YELLOW}Running unit tests...${NC}"
pytest tests/unit -v --tb=short

UNIT_EXIT=$?

# Run integration tests
echo -e "${YELLOW}Running integration tests...${NC}"
pytest tests/integration -v --tb=short

INTEGRATION_EXIT=$?

# Run coverage report
echo -e "${YELLOW}Generating coverage report...${NC}"
pytest --cov=. --cov-report=html --cov-report=term-missing tests/

# Summary
echo -e "\n${YELLOW}Test Summary:${NC}"
if [ $UNIT_EXIT -eq 0 ]; then
    echo -e "${GREEN}✓ Unit tests passed${NC}"
else
    echo -e "${RED}✗ Unit tests failed${NC}"
fi

if [ $INTEGRATION_EXIT -eq 0 ]; then
    echo -e "${GREEN}✓ Integration tests passed${NC}"
else
    echo -e "${RED}✗ Integration tests failed${NC}"
fi

# Exit with error if any tests failed
if [ $UNIT_EXIT -ne 0 ] || [ $INTEGRATION_EXIT -ne 0 ]; then
    exit 1
fi

echo -e "\n${GREEN}✅ All tests passed!${NC}"