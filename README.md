# Biormika HFO Backend

Serverless backend for the Biormika High-Frequency Oscillation (HFO) detection system. This backend processes EDF files to detect HFO patterns in EEG data.

## Architecture

Built using AWS Serverless technologies:
- **AWS Lambda** - Serverless compute for all processing
- **API Gateway** - REST API and WebSocket endpoints
- **S3** - File storage for EDF uploads and results
- **DynamoDB** - Metadata and state management
- **Cognito** - User authentication and authorization
- **Step Functions** - Orchestration of analysis workflow
- **CloudWatch** - Monitoring and logging

## Project Structure

```
Biormika-AWS-Backend/
├── auth/               # Authentication handlers
├── files/              # File upload/validation handlers
├── analysis/           # Core analysis modules
│   ├── core/          # HFO detection algorithms
│   ├── edf/           # EDF file processing
│   └── tasks.py       # Step Function task handlers
├── visualization/      # Plot and report generation
├── websocket/         # Real-time update handlers
├── storage/           # S3 and DynamoDB utilities
├── utils/             # Error handling and logging
├── docs/              # API documentation
└── serverless.yml     # Infrastructure configuration
```

## Quick Start

### Prerequisites
- Node.js 14+
- Python 3.9
- AWS CLI configured
- Docker (for local development)

### Installation

```bash
# Install dependencies
npm install
pip install -r requirements.txt

# Configure environment
cp .env.example .env.dev
# Edit .env.dev with your configuration

# Deploy to AWS
serverless deploy --stage dev
```

### Local Development

```bash
# Set up local environment (first time)
npm run setup:local

# Start LocalStack and services
docker-compose up -d

# Run serverless offline
npm run offline

# API available at http://localhost:3001
# WebSocket at ws://localhost:3002
# DynamoDB Admin UI at http://localhost:8001
```

## API Overview

### Authentication
- `POST /auth/login` - User login
- `POST /auth/signup` - User registration
- `POST /auth/refresh` - Token refresh

### File Management
- `POST /files/upload/init` - Initialize file upload
- `POST /files/validate/{fileId}` - Validate EDF file

### Analysis
- `POST /analysis/start` - Start HFO analysis
- `GET /analysis/status/{analysisId}` - Get progress
- `GET /analysis/results/{analysisId}` - Get results

### WebSocket
- Real-time analysis progress updates
- Subscribe to specific analysis events

## Key Features

### HFO Detection
- Configurable detection thresholds
- Multiple montage options (bipolar, average, referential)
- Frequency filtering (ripples: 80-250Hz, fast ripples: 250-500Hz)
- Synchronized event detection across channels

### File Processing
- EDF format validation
- Streaming processing for large files
- Automatic channel extraction and montage application

### Result Generation
- Comprehensive HFO statistics
- Channel-wise analysis results
- Visualization plots (overview, channel-specific, synchronization)
- PDF report generation

## Configuration

### Analysis Parameters

```json
{
  "thresholdSettings": {
    "amplitude1": 5,      // Primary amplitude threshold
    "amplitude2": 3,      // Secondary amplitude threshold
    "peaks1": 6,          // Minimum peaks
    "peaks2": 3,          // Minimum high-amplitude peaks
    "duration": 10        // Minimum duration (ms)
  },
  "frequencyFilterSettings": {
    "lowCutoffFilter": 80,   // High-pass filter (Hz)
    "highCutoffFilter": 250  // Low-pass filter (Hz)
  }
}
```

## Security

- JWT-based authentication
- Row-level security in DynamoDB
- Presigned URLs for secure file uploads
- API Gateway request validation
- CloudWatch monitoring and alerting

## Performance

- Parallel processing with Step Functions
- Optimized Lambda memory allocation
- S3 streaming for large files
- DynamoDB auto-scaling
- CloudFront CDN for static assets

## Testing

```bash
# Install test dependencies
pip install -r requirements.txt

# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests only
npm run test:integration

# Run with coverage report
npm run test:coverage

# Test specific endpoint locally
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

### Test Structure
- `tests/unit/` - Unit tests for individual components
- `tests/integration/` - End-to-end workflow tests
- `tests/conftest.py` - Shared test fixtures and configuration

## Deployment

See [deployment.md](docs/deployment.md) for detailed deployment instructions.

## API Documentation

Complete API documentation available in [api.md](docs/api.md).

## Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## License

Proprietary - Biormika Ltd. All rights reserved.