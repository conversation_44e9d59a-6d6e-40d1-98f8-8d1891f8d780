# Biormika HFO Backend Deployment Guide

## Prerequisites

1. **AWS Account** with appropriate permissions
2. **AWS CLI** configured with credentials
3. **Node.js** (v14 or higher) and npm
4. **Python 3.9** runtime
5. **Docker** (for local development and Lambda layer building)

## Initial Setup

### 1. Install Dependencies

```bash
# Install Node.js dependencies
npm install

# Create Python virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install Python dependencies
pip install -r requirements.txt
```

### 2. Configure Environment

Create environment file for your stage:

```bash
# For development
cp .env.example .env.dev

# For production  
cp .env.example .env.prod
```

Edit the environment file with your configuration:
- `JWT_SECRET`: Strong secret key for JWT signing
- `AWS_REGION`: Your preferred AWS region
- `AWS_PROFILE`: AWS CLI profile to use

### 3. AWS Setup

Ensure you have the following AWS services available:
- Lambda
- API Gateway
- S3
- DynamoDB
- Cognito
- Step Functions
- CloudWatch

## Deployment

### Development Environment

```bash
# Deploy to development stage
serverless deploy --stage dev
```

### Production Environment

```bash
# Deploy to production stage
serverless deploy --stage prod
```

### Deploy Specific Function

```bash
# Deploy only a specific function
serverless deploy function -f functionName --stage dev
```

## Post-Deployment Configuration

### 1. Note Output Values

After deployment, Serverless will output important values:

```
Service Information
service: biormika-hfo-backend
stage: dev
region: us-east-1
stack: biormika-hfo-backend-dev
resources: 45
api keys:
  None
endpoints:
  POST - https://xxxxxx.execute-api.us-east-1.amazonaws.com/dev/auth/login
  POST - https://xxxxxx.execute-api.us-east-1.amazonaws.com/dev/auth/signup
  ...
```

Save these values for frontend configuration.

### 2. Configure Frontend

Update your React frontend with the API endpoints:

```javascript
// Frontend .env file
REACT_APP_API_URL=https://xxxxxx.execute-api.us-east-1.amazonaws.com/dev
REACT_APP_WS_URL=wss://yyyyyy.execute-api.us-east-1.amazonaws.com/dev
```

### 3. Cognito Configuration

1. Go to AWS Cognito Console
2. Select your user pool
3. Configure app client settings:
   - Enable identity providers
   - Configure callback URLs
   - Set OAuth 2.0 settings

### 4. S3 CORS Configuration

The S3 bucket CORS is configured automatically via serverless.yml, but verify:

```json
{
  "CORSRules": [
    {
      "AllowedHeaders": ["*"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
      "AllowedOrigins": ["*"],
      "MaxAge": 3000
    }
  ]
}
```

## Monitoring and Logs

### View Function Logs

```bash
# View logs for specific function
serverless logs -f functionName --stage dev

# Tail logs
serverless logs -f functionName --stage dev -t
```

### CloudWatch Dashboard

1. Go to AWS CloudWatch Console
2. Create dashboard for monitoring:
   - Lambda invocations
   - Error rates
   - Duration metrics
   - API Gateway requests

### Set Up Alarms

Create CloudWatch alarms for:
- Lambda errors > threshold
- API Gateway 4xx/5xx errors
- DynamoDB throttling
- Step Functions failures

## Local Development

### 1. Start Local Services

```bash
# Start serverless offline
npm run offline

# API will be available at http://localhost:3001
# WebSocket at ws://localhost:3002
```

### 2. LocalStack Setup (Optional)

For complete local AWS simulation:

```bash
# Install LocalStack
pip install localstack

# Start LocalStack
localstack start

# Configure serverless-localstack plugin
npm install --save-dev serverless-localstack
```

### 3. Testing

```bash
# Run unit tests
python -m pytest tests/

# Run integration tests
npm test
```

## Troubleshooting

### Common Issues

1. **Lambda Timeout**
   - Increase timeout in serverless.yml
   - Optimize code for performance
   - Consider using Step Functions for long-running tasks

2. **CORS Errors**
   - Verify CORS configuration in serverless.yml
   - Check API Gateway CORS settings
   - Ensure frontend sends proper headers

3. **Authentication Failures**
   - Verify Cognito user pool configuration
   - Check JWT secret in environment variables
   - Ensure token is properly formatted

4. **File Upload Issues**
   - Check S3 bucket permissions
   - Verify presigned URL generation
   - Ensure file size limits are appropriate

### Debug Mode

Enable debug logging:

```bash
# Set environment variable
export LOG_LEVEL=DEBUG

# Deploy with debug
serverless deploy --stage dev --verbose
```

## Rollback

### Quick Rollback

```bash
# Rollback to previous deployment
serverless rollback --stage dev
```

### Specific Version Rollback

```bash
# List deployments
serverless deploy list --stage dev

# Rollback to specific timestamp
serverless rollback --timestamp 1234567890 --stage dev
```

## Security Considerations

1. **Environment Variables**
   - Never commit .env files
   - Use AWS Systems Manager for production secrets
   - Rotate JWT secrets regularly

2. **API Security**
   - Enable API Gateway throttling
   - Implement request validation
   - Use AWS WAF for additional protection

3. **Data Protection**
   - Enable S3 encryption at rest
   - Use HTTPS for all communications
   - Implement data retention policies

## Performance Optimization

1. **Lambda Configuration**
   - Adjust memory allocation based on usage
   - Use provisioned concurrency for consistent performance
   - Enable Lambda caching where appropriate

2. **API Gateway**
   - Enable caching for GET requests
   - Use compression for responses
   - Implement pagination for large results

3. **Database**
   - Use DynamoDB auto-scaling
   - Implement efficient query patterns
   - Cache frequently accessed data

## Cost Management

1. **Monitor Usage**
   - Set up AWS Cost Explorer
   - Create billing alerts
   - Review Lambda invocation patterns

2. **Optimize Resources**
   - Right-size Lambda functions
   - Clean up old S3 objects
   - Use lifecycle policies

3. **Reserved Capacity**
   - Consider reserved capacity for DynamoDB
   - Use savings plans for predictable workloads