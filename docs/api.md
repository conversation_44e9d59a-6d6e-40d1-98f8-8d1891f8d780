# Biormika HFO Backend API Documentation

## Overview

The Biormika HFO Backend is a serverless API that provides EDF file analysis for High-Frequency Oscillation (HFO) detection. Built on AWS Lambda, it offers secure file uploads, real-time analysis progress tracking, and comprehensive result visualization.

## Base URL

```
Production: https://api.biormika.com
Development: http://localhost:3001
```

## Authentication

All endpoints except login and signup require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### Authentication

#### POST /auth/login
Login with email and password.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "role": "user",
      "permissions": ["read", "write"]
    },
    "token": "jwt-token"
  },
  "statusCode": 200
}
```

#### POST /auth/signup
Create a new user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "fullName": "<PERSON> Doe"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "user",
      "permissions": ["read", "write"]
    },
    "token": "jwt-token"
  },
  "statusCode": 201
}
```

#### POST /auth/refresh
Refresh authentication token.

**Request:**
```json
{
  "refreshToken": "refresh-token"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "idToken": "new-id-token",
    "accessToken": "new-access-token"
  },
  "statusCode": 200
}
```

### File Management

#### POST /files/upload/init
Initialize file upload and get presigned URL.

**Request:**
```json
{
  "fileName": "patient_data.edf",
  "fileSize": ********,
  "fileType": "application/octet-stream"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "fileId": "file-uuid",
    "uploadUrl": "https://s3.amazonaws.com/bucket",
    "fields": {
      "key": "users/user-id/uploads/timestamp_file-id.edf",
      "Content-Type": "application/octet-stream",
      "x-amz-meta-user-id": "user-id"
    },
    "expires": 3600
  },
  "statusCode": 200
}
```

#### POST /files/validate/{fileId}
Validate uploaded EDF file.

**Response:**
```json
{
  "success": true,
  "data": {
    "fileId": "file-uuid",
    "status": "valid",
    "fileInfo": {
      "channels": ["Fp1", "Fp2", "F3", "F4"],
      "numChannels": 4,
      "samplingRate": 256,
      "duration": 3600,
      "numRecords": 3600
    }
  },
  "statusCode": 200
}
```

### Analysis

#### POST /analysis/start
Start HFO analysis on validated file.

**Request:**
```json
{
  "fileId": "file-uuid",
  "settings": {
    "thresholdSettings": {
      "amplitude1": 5,
      "amplitude2": 3,
      "peaks1": 6,
      "peaks2": 3,
      "duration": 10
    },
    "synchronizationSettings": {
      "temporalSync": 10,
      "spatialSync": 0
    },
    "montageSelection": {
      "bipolar": true,
      "average": false,
      "referential": false
    },
    "frequencyFilterSettings": {
      "lowCutoffFilter": 80,
      "highCutoffFilter": 250
    },
    "segmentSelectionSettings": {
      "entireFile": true,
      "startTime": 0,
      "endTime": -1
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "analysisId": "analysis-uuid",
    "status": "started",
    "executionArn": "arn:aws:states:region:account:execution:name"
  },
  "statusCode": 200
}
```

#### GET /analysis/status/{analysisId}
Get analysis progress and status.

**Response:**
```json
{
  "success": true,
  "data": {
    "analysisId": "analysis-uuid",
    "status": "running",
    "progress": {
      "stage": "detecting_hfos",
      "percentage": 40
    },
    "createdAt": "2024-01-15T10:30:00Z"
  },
  "statusCode": 200
}
```

#### GET /analysis/results/{analysisId}
Retrieve analysis results.

**Response:**
```json
{
  "success": true,
  "data": {
    "analysisId": "analysis-uuid",
    "results": {
      "totalHfos": 125,
      "hfoRate": 0.0347,
      "analysisDuration": 3600,
      "channels": [
        {
          "channelIndex": 0,
          "hfoCount": 35,
          "hfoRate": 0.0097,
          "events": [...]
        }
      ]
    },
    "reportUrl": "https://s3.amazonaws.com/presigned-url-to-pdf",
    "visualizations": [
      "https://s3.amazonaws.com/presigned-url-to-image1",
      "https://s3.amazonaws.com/presigned-url-to-image2"
    ]
  },
  "statusCode": 200
}
```

### WebSocket API

Connect to real-time updates at: `wss://api.biormika.com/ws`

#### Subscribe to Analysis Updates
```json
{
  "action": "subscribe",
  "analysisId": "analysis-uuid",
  "userId": "user-id"
}
```

#### Unsubscribe from Updates
```json
{
  "action": "unsubscribe",
  "analysisId": "analysis-uuid"
}
```

#### Ping/Pong
```json
{
  "action": "ping"
}
```

## Error Responses

All errors follow this format:
```json
{
  "success": false,
  "message": "Error description",
  "error": "ErrorType",
  "statusCode": 400
}
```

Common error codes:
- 400: Bad Request - Invalid input parameters
- 401: Unauthorized - Missing or invalid authentication
- 403: Forbidden - Insufficient permissions
- 404: Not Found - Resource not found
- 409: Conflict - Resource already exists
- 429: Too Many Requests - Rate limit exceeded
- 500: Internal Server Error - Server-side error
- 503: Service Unavailable - Service temporarily unavailable

## Rate Limits

- Authentication endpoints: 5 requests per minute per IP
- File upload initialization: 10 requests per minute per user
- Analysis endpoints: 20 requests per minute per user
- WebSocket connections: 5 concurrent connections per user

## File Requirements

- Maximum file size: 100MB
- Supported format: EDF (European Data Format) only
- File extension must be .edf
- Files must contain valid EDF header and data

## Analysis Parameters

### Threshold Settings
- `amplitude1`: Primary amplitude threshold (2-10)
- `amplitude2`: Secondary amplitude threshold (1-5)
- `peaks1`: Minimum number of peaks (4-10)
- `peaks2`: Minimum high-amplitude peaks (2-6)
- `duration`: Minimum HFO duration in ms (6-20)

### Synchronization Settings
- `temporalSync`: Time window for synchronized events in ms (0-50)
- `spatialSync`: Spatial synchronization delay in ms (0-10)

### Montage Selection
- `bipolar`: Use bipolar montage
- `average`: Use average reference montage
- `referential`: Use referential montage

### Frequency Filter Settings
- `lowCutoffFilter`: High-pass filter frequency in Hz (50-100)
- `highCutoffFilter`: Low-pass filter frequency in Hz (200-500)

### Segment Selection Settings
- `entireFile`: Analyze entire file (true/false)
- `startTime`: Analysis start time in seconds
- `endTime`: Analysis end time in seconds (-1 for end of file)