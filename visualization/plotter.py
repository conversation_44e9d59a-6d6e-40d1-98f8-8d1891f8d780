import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
import boto3
import os
import tempfile
from datetime import datetime

s3 = boto3.client('s3')
S3_BUCKET = os.environ.get('S3_BUCKET')

def generate_hfo_plots(eeg_data, hfo_results, header, analysis_id, user_id):
    visualization_keys = []
    
    try:
        sampling_rate = header['samplingRate']
        channels = header['channels']
        
        overview_key = create_overview_plot(hfo_results, channels, analysis_id, user_id)
        visualization_keys.append(overview_key)
        
        for channel_idx, channel_info in enumerate(hfo_results['channels']):
            if channel_info['hfo_count'] > 0:
                channel_key = create_channel_plot(
                    eeg_data[channel_idx],
                    channel_info,
                    channels[channel_idx],
                    sampling_rate,
                    analysis_id,
                    user_id,
                    channel_idx
                )
                visualization_keys.append(channel_key)
                
                if len(visualization_keys) >= 10:
                    break
        
        if hfo_results.get('synchronized_events'):
            sync_key = create_synchronization_plot(
                hfo_results['synchronized_events'],
                channels,
                analysis_id,
                user_id
            )
            visualization_keys.append(sync_key)
        
        return visualization_keys
        
    except Exception as e:
        print(f"Error generating plots: {e}")
        raise e

def create_overview_plot(hfo_results, channels, analysis_id, user_id):
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    channel_counts = [ch['hfo_count'] for ch in hfo_results['channels']]
    channel_rates = [ch['hfo_rate'] for ch in hfo_results['channels']]
    
    x = np.arange(len(channels))
    
    ax1.bar(x, channel_counts, color='steelblue', alpha=0.8)
    ax1.set_xlabel('Channel')
    ax1.set_ylabel('HFO Count')
    ax1.set_title('HFO Detection Summary by Channel')
    ax1.set_xticks(x)
    ax1.set_xticklabels(channels, rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)
    
    ax2.bar(x, channel_rates, color='darkgreen', alpha=0.8)
    ax2.set_xlabel('Channel')
    ax2.set_ylabel('HFO Rate (events/second)')
    ax2.set_title('HFO Rate by Channel')
    ax2.set_xticks(x)
    ax2.set_xticklabels(channels, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    plt.suptitle(f'HFO Analysis Overview - Total: {hfo_results["total_hfos"]} HFOs', fontsize=14)
    plt.tight_layout()
    
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
        plt.savefig(tmp.name, dpi=150, bbox_inches='tight')
        plt.close()
        
        s3_key = f"users/{user_id}/temp/{analysis_id}/overview.png"
        s3.upload_file(tmp.name, S3_BUCKET, s3_key)
        os.unlink(tmp.name)
        
    return s3_key

def create_channel_plot(channel_data, channel_info, channel_name, sampling_rate, analysis_id, user_id, channel_idx):
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 8), sharex=True)
    
    time = np.arange(len(channel_data)) / sampling_rate
    
    ax1.plot(time, channel_data, 'b-', linewidth=0.5, alpha=0.8)
    ax1.set_ylabel('Amplitude (μV)')
    ax1.set_title(f'Raw EEG Signal - {channel_name}')
    ax1.grid(True, alpha=0.3)
    
    for event in channel_info['events']:
        start_time = event['start_time']
        end_time = event['end_time']
        
        ax1.axvspan(start_time, end_time, alpha=0.3, color='red', label='HFO' if event == channel_info['events'][0] else '')
        ax2.axvspan(start_time, end_time, alpha=0.3, color='red')
    
    from scipy.signal import butter, filtfilt
    nyquist = sampling_rate / 2
    low = 80 / nyquist
    high = 250 / nyquist
    b, a = butter(3, [low, high], btype='band')
    filtered_data = filtfilt(b, a, channel_data)
    
    ax2.plot(time, filtered_data, 'g-', linewidth=0.5, alpha=0.8)
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Amplitude (μV)')
    ax2.set_title(f'Filtered Signal (80-250 Hz) - {channel_info["hfo_count"]} HFOs detected')
    ax2.grid(True, alpha=0.3)
    
    if channel_info['events']:
        ax1.legend(loc='upper right')
    
    plt.tight_layout()
    
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
        plt.savefig(tmp.name, dpi=150, bbox_inches='tight')
        plt.close()
        
        s3_key = f"users/{user_id}/temp/{analysis_id}/channel_{channel_idx}.png"
        s3.upload_file(tmp.name, S3_BUCKET, s3_key)
        os.unlink(tmp.name)
        
    return s3_key

def create_synchronization_plot(synchronized_events, channels, analysis_id, user_id):
    fig, ax = plt.subplots(figsize=(12, 8))
    
    channel_indices = {ch: i for i, ch in enumerate(channels)}
    
    for i, sync_group in enumerate(synchronized_events[:20]):
        for event in sync_group['events']:
            channel_idx = channel_indices.get(channels[event['channel']], event['channel'])
            start_time = event['start_time']
            duration = event['duration_ms'] / 1000
            
            rect = plt.Rectangle((start_time, channel_idx - 0.4), duration, 0.8,
                               facecolor=f'C{i % 10}', alpha=0.7, edgecolor='black', linewidth=1)
            ax.add_patch(rect)
    
    ax.set_ylim(-1, len(channels))
    ax.set_yticks(range(len(channels)))
    ax.set_yticklabels(channels)
    ax.set_xlabel('Time (s)')
    ax.set_title(f'Synchronized HFO Events - {len(synchronized_events)} groups detected')
    ax.grid(True, alpha=0.3, axis='x')
    
    plt.tight_layout()
    
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
        plt.savefig(tmp.name, dpi=150, bbox_inches='tight')
        plt.close()
        
        s3_key = f"users/{user_id}/temp/{analysis_id}/synchronization.png"
        s3.upload_file(tmp.name, S3_BUCKET, s3_key)
        os.unlink(tmp.name)
        
    return s3_key