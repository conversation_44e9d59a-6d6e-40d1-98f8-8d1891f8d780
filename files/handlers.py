import json
import os
import uuid
from datetime import datetime
from auth.decorators import auth_required
from storage.presigned_urls import generate_upload_url, get_file_metadata as get_s3_metadata
from storage.metadata import save_file_metadata, get_file_metadata, update_file_status
from files.validators import validate_edf_file

def create_response(status_code, body):
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
        },
        'body': json.dumps(body)
    }

@auth_required
def init_upload(event, context):
    try:
        body = json.loads(event['body'])
        file_name = body.get('fileName')
        file_size = body.get('fileSize')
        file_type = body.get('fileType', 'application/octet-stream')
        
        if not file_name or not file_size:
            return create_response(400, {
                'success': False,
                'message': 'File name and size are required',
                'statusCode': 400
            })
        
        if file_size > 104857600:  # 100MB limit
            return create_response(400, {
                'success': False,
                'message': 'File size exceeds 100MB limit',
                'statusCode': 400
            })
        
        if not file_name.lower().endswith('.edf'):
            return create_response(400, {
                'success': False,
                'message': 'Only EDF files are supported',
                'statusCode': 400
            })
        
        user = event.get('user', {})
        user_id = user.get('id')
        
        try:
            upload_data = generate_upload_url(
                file_name=file_name,
                file_type=file_type,
                user_id=user_id
            )
            
            file_metadata = save_file_metadata({
                'fileId': upload_data['fileId'],
                'fileName': file_name,
                'fileSize': file_size,
                's3Key': upload_data['s3Key'],
                'contentType': file_type,
                'status': 'pending',
                'userId': user_id
            })
            
            return create_response(200, {
                'success': True,
                'data': {
                    'fileId': upload_data['fileId'],
                    'uploadUrl': upload_data['uploadUrl'],
                    'fields': upload_data['fields'],
                    'expires': upload_data['expires']
                },
                'message': 'Upload URL generated successfully',
                'statusCode': 200
            })
            
        except Exception as e:
            print(f"Error initializing upload: {e}")
            return create_response(500, {
                'success': False,
                'message': 'Failed to initialize upload',
                'statusCode': 500
            })
            
    except json.JSONDecodeError:
        return create_response(400, {
            'success': False,
            'message': 'Invalid request body',
            'statusCode': 400
        })
    except Exception as e:
        print(f"Init upload error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })

@auth_required
def validate_file(event, context):
    try:
        file_id = event['pathParameters'].get('fileId')
        
        if not file_id:
            return create_response(400, {
                'success': False,
                'message': 'File ID is required',
                'statusCode': 400
            })
        
        user = event.get('user', {})
        user_id = user.get('id')
        
        file_metadata = get_file_metadata(file_id)
        
        if not file_metadata:
            return create_response(404, {
                'success': False,
                'message': 'File not found',
                'statusCode': 404
            })
        
        if file_metadata['userId'] != user_id:
            return create_response(403, {
                'success': False,
                'message': 'Unauthorized access to file',
                'statusCode': 403
            })
        
        update_file_status(file_id, 'validating')
        
        try:
            s3_metadata = get_s3_metadata(file_metadata['s3Key'])
            
            if not s3_metadata:
                update_file_status(file_id, 'error', {'errorMessage': 'File not found in storage'})
                return create_response(404, {
                    'success': False,
                    'message': 'File not found in storage',
                    'statusCode': 404
                })
            
            validation_result = validate_edf_file(file_metadata['s3Key'])
            
            if validation_result['valid']:
                update_file_status(file_id, 'valid', {
                    'validationResult': validation_result,
                    'fileInfo': {
                        'channels': validation_result.get('channels', []),
                        'duration': validation_result.get('duration'),
                        'samplingRate': validation_result.get('samplingRate')
                    }
                })
                
                return create_response(200, {
                    'success': True,
                    'data': {
                        'fileId': file_id,
                        'status': 'valid',
                        'fileInfo': validation_result
                    },
                    'message': 'File validated successfully',
                    'statusCode': 200
                })
            else:
                update_file_status(file_id, 'error', {
                    'errorMessage': validation_result.get('error', 'Invalid EDF file format')
                })
                
                return create_response(400, {
                    'success': False,
                    'message': validation_result.get('error', 'Invalid EDF file format'),
                    'statusCode': 400
                })
                
        except Exception as e:
            print(f"Validation error: {e}")
            update_file_status(file_id, 'error', {'errorMessage': str(e)})
            return create_response(500, {
                'success': False,
                'message': 'Failed to validate file',
                'statusCode': 500
            })
            
    except Exception as e:
        print(f"Validate file error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })

@auth_required
def get_upload_status(event, context):
    try:
        file_id = event['pathParameters'].get('fileId')
        
        if not file_id:
            return create_response(400, {
                'success': False,
                'message': 'File ID is required',
                'statusCode': 400
            })
        
        user = event.get('user', {})
        user_id = user.get('id')
        
        file_metadata = get_file_metadata(file_id)
        
        if not file_metadata:
            return create_response(404, {
                'success': False,
                'message': 'File not found',
                'statusCode': 404
            })
        
        if file_metadata['userId'] != user_id:
            return create_response(403, {
                'success': False,
                'message': 'Unauthorized access to file',
                'statusCode': 403
            })
        
        return create_response(200, {
            'success': True,
            'data': {
                'fileId': file_metadata['fileId'],
                'fileName': file_metadata['fileName'],
                'fileSize': file_metadata['fileSize'],
                'status': file_metadata['status'],
                'uploadedAt': file_metadata['uploadedAt'],
                'fileInfo': file_metadata.get('fileInfo')
            },
            'message': 'File status retrieved successfully',
            'statusCode': 200
        })
        
    except Exception as e:
        print(f"Get upload status error: {str(e)}")
        return create_response(500, {
            'success': False,
            'message': 'Internal server error',
            'statusCode': 500
        })