import json
import functools
from jose import jwt, JWTError
import os

JWT_SECRET = os.environ.get('JWT_SECRET', 'your-secret-key')
JWT_ALGORITHM = 'HS256'

def create_response(status_code, body):
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Credentials': True,
        },
        'body': json.dumps(body)
    }

def auth_required(handler):
    @functools.wraps(handler)
    def wrapper(event, context):
        authorization_header = event.get('headers', {}).get('Authorization', '')
        
        if not authorization_header:
            return create_response(401, {
                'success': False,
                'message': 'Authorization header is required',
                'statusCode': 401
            })
        
        try:
            token = authorization_header.replace('Bearer ', '')
            payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
            
            event['user'] = {
                'id': payload.get('sub'),
                'email': payload.get('email'),
                'name': payload.get('name'),
                'role': payload.get('role')
            }
            
            return handler(event, context)
            
        except JWTError as e:
            print(f"JWT decode error: {str(e)}")
            return create_response(401, {
                'success': False,
                'message': 'Invalid or expired token',
                'statusCode': 401
            })
        except Exception as e:
            print(f"Auth error: {str(e)}")
            return create_response(500, {
                'success': False,
                'message': 'Authentication error',
                'statusCode': 500
            })
    
    return wrapper

def role_required(required_role):
    def decorator(handler):
        @functools.wraps(handler)
        @auth_required
        def wrapper(event, context):
            user = event.get('user', {})
            user_role = user.get('role', 'user')
            
            if user_role != required_role and user_role != 'admin':
                return create_response(403, {
                    'success': False,
                    'message': f'Insufficient permissions. Required role: {required_role}',
                    'statusCode': 403
                })
            
            return handler(event, context)
        
        return wrapper
    
    return decorator